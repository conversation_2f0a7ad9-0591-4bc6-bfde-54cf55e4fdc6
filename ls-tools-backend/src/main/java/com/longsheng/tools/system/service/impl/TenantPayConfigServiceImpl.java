package com.longsheng.tools.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.longsheng.tools.common.utils.PageUtils;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.TenantPayConfig;
import com.longsheng.tools.system.mapper.TenantPayConfigMapper;
import com.longsheng.tools.system.service.TenantPayConfigService;
import com.longsheng.tools.system.vo.PageVO;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;

/**
 * <p>
 * 租户支付配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class TenantPayConfigServiceImpl extends ServiceImpl<TenantPayConfigMapper, TenantPayConfig> implements TenantPayConfigService {

    /**
     * 查询租户支付配置列表
     *
     * @param tenantPayConfig 租户支付配置
     * @param isExport 是否导出，0-否，1-是
     * @return 分页结果
     */
    @Override
    public TableInfo list(TenantPayConfig tenantPayConfig, int isExport) {
        LambdaQueryWrapper<TenantPayConfig> queryWrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (!StringUtils.isEmpty(tenantPayConfig.getAppId())) {
            queryWrapper.like(TenantPayConfig::getAppId, tenantPayConfig.getAppId());
        }

        if (!StringUtils.isEmpty(tenantPayConfig.getMerchantId())) {
            queryWrapper.like(TenantPayConfig::getMerchantId, tenantPayConfig.getMerchantId());
        }

        if (tenantPayConfig.getTenantId() != null) {
            queryWrapper.eq(TenantPayConfig::getTenantId, tenantPayConfig.getTenantId());
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(TenantPayConfig::getCreateDate);
        
        PageVO pageVO = PageUtils.getPageVO();
        Page<TenantPayConfig> page = this.page(new Page<>(pageVO.getPageNum(), pageVO.getPageSize()), queryWrapper);
        return TableInfo.ok(page);
    }

    /**
     * 获取租户支付配置详情
     *
     * @param id 租户支付配置ID
     * @return 租户支付配置信息
     */
    @Override
    public RES getTenantPayConfig(Integer id) {
        TenantPayConfig tenantPayConfig = this.getById(id);
        if (tenantPayConfig == null) {
            return RES.no("租户支付配置不存在");
        }
        return RES.ok(tenantPayConfig);
    }

    /**
     * 新增租户支付配置
     *
     * @param tenantPayConfig 租户支付配置信息
     * @return 结果
     */
    @Override
    public RES add(TenantPayConfig tenantPayConfig) {
        // 参数校验
        if (StringUtils.isEmpty(tenantPayConfig.getAppId())) {
            return RES.no("应用ID不能为空");
        }
        if (StringUtils.isEmpty(tenantPayConfig.getMerchantId())) {
            return RES.no("商户ID不能为空");
        }
        if (tenantPayConfig.getTenantId() == null) {
            return RES.no("租户ID不能为空");
        }

        // 检查租户是否已存在支付配置
        LambdaQueryWrapper<TenantPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPayConfig::getTenantId, tenantPayConfig.getTenantId());
        long count = this.count(queryWrapper);
        if (count > 0) {
            return RES.no("该租户已存在支付配置");
        }

        // 设置创建时间
        if (tenantPayConfig.getCreateDate() == null) {
            tenantPayConfig.setCreateDate(new Date());
        }

        boolean result = this.save(tenantPayConfig);
        if (result) {
            return RES.ok();
        } else {
            return RES.no("新增租户支付配置失败");
        }
    }

    /**
     * 修改租户支付配置
     *
     * @param tenantPayConfig 租户支付配置信息
     * @return 结果
     */
    @Override
    public RES update(TenantPayConfig tenantPayConfig) {
        if (tenantPayConfig.getId() == null) {
            return RES.no("租户支付配置ID不能为空");
        }

        // 参数校验
        if (StringUtils.isEmpty(tenantPayConfig.getAppId())) {
            return RES.no("应用ID不能为空");
        }
        if (StringUtils.isEmpty(tenantPayConfig.getMerchantId())) {
            return RES.no("商户ID不能为空");
        }
        if (tenantPayConfig.getTenantId() == null) {
            return RES.no("租户ID不能为空");
        }

        // 检查是否存在其他租户使用相同配置
        LambdaQueryWrapper<TenantPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPayConfig::getTenantId, tenantPayConfig.getTenantId());
        queryWrapper.ne(TenantPayConfig::getId, tenantPayConfig.getId());
        long count = this.count(queryWrapper);
        if (count > 0) {
            return RES.no("该租户已存在其他支付配置");
        }

        boolean result = this.updateById(tenantPayConfig);
        if (result) {
            return RES.ok();
        } else {
            return RES.no("修改租户支付配置失败");
        }
    }

    /**
     * 删除租户支付配置
     *
     * @param ids 需要删除的租户支付配置ID数组
     * @return 结果
     */
    @Override
    public RES delete(Integer[] ids) {
        if (ids == null || ids.length == 0) {
            return RES.no("请选择需要删除的数据");
        }

        boolean result = this.removeByIds(Arrays.asList(ids));
        if (result) {
            return RES.ok();
        } else {
            return RES.no("删除租户支付配置失败");
        }
    }

    /**
     * 根据租户ID获取支付配置
     *
     * @param tenantId 租户ID
     * @return 支付配置信息
     */
    @Override
    public TenantPayConfig getByTenantId(Integer tenantId) {
        LambdaQueryWrapper<TenantPayConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantPayConfig::getTenantId, tenantId);
        return this.getOne(queryWrapper);
    }
}
