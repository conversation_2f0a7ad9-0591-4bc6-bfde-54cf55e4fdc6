package com.longsheng.tools.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.longsheng.tools.common.base.TenantBaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 字典管理
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_dict")
public class SysDict extends Model<SysDict> {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("code")
    private String code;

    @TableField("name")
    private String name;

    @TableField("remake")
    private String remake;

    @TableField("create_user")
    private Integer createUser;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_user")
    private Integer updateUser;

    @TableField("update_time")
    private Date updateTime;

    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
