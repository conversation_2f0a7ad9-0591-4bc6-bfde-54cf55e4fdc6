package com.longsheng.tools.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.longsheng.tools.common.annotation.SysLog;
import com.longsheng.tools.common.enums.BusinessType;
import com.longsheng.tools.common.excel.ExportExcelUtil;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.TenantPayConfig;
import com.longsheng.tools.system.service.TenantPayConfigService;
import com.longsheng.tools.system.vo.TableInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 租户支付配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/tenant-pay-config")
public class TenantPayConfigController {

    @Autowired
    private TenantPayConfigService tenantPayConfigService;

    /**
     * 查询租户支付配置列表
     */
    @GetMapping("/list")
    @SaCheckPermission("system:tenantPayConfig:list")
    public TableInfo list(TenantPayConfig tenantPayConfig) {
        return tenantPayConfigService.list(tenantPayConfig, 0);
    }

    /**
     * 导出租户支付配置列表
     */
    @PostMapping("/export")
    @SaCheckPermission("system:tenantPayConfig:export")
    @SysLog(title = "租户支付配置", businessType = BusinessType.EXPORT)
    public void export(TenantPayConfig tenantPayConfig) {
        TableInfo tableInfo = tenantPayConfigService.list(tenantPayConfig, 1);
        List<TenantPayConfig> list = (List<TenantPayConfig>) tableInfo.getData();
        ExportExcelUtil.exportExcel(list, "租户支付配置", TenantPayConfig.class);
    }

    /**
     * 获取租户支付配置详细信息
     */
    @GetMapping(value = "/{id}")
    @SaCheckPermission("system:tenantPayConfig:query")
    public RES getInfo(@PathVariable("id") Integer id) {
        return tenantPayConfigService.getTenantPayConfig(id);
    }

    /**
     * 新增租户支付配置
     */
    @PostMapping
    @SaCheckPermission("system:tenantPayConfig:add")
    @SysLog(title = "租户支付配置", businessType = BusinessType.INSERT)
    public RES add(@RequestBody TenantPayConfig tenantPayConfig) {
        return tenantPayConfigService.add(tenantPayConfig);
    }

    /**
     * 修改租户支付配置
     */
    @PutMapping
    @SaCheckPermission("system:tenantPayConfig:edit")
    @SysLog(title = "租户支付配置", businessType = BusinessType.UPDATE)
    public RES edit(@RequestBody TenantPayConfig tenantPayConfig) {
        return tenantPayConfigService.update(tenantPayConfig);
    }

    /**
     * 删除租户支付配置
     */
    @DeleteMapping("/{ids}")
    @SaCheckPermission("system:tenantPayConfig:remove")
    @SysLog(title = "租户支付配置", businessType = BusinessType.DELETE)
    public RES remove(@PathVariable Integer[] ids) {
        return tenantPayConfigService.delete(ids);
    }

    /**
     * 根据租户ID获取支付配置
     */
    @GetMapping("/tenant/{tenantId}")
    @SaCheckPermission("system:tenantPayConfig:query")
    public RES getByTenantId(@PathVariable("tenantId") Integer tenantId) {
        TenantPayConfig config = tenantPayConfigService.getByTenantId(tenantId);
        if (config == null) {
            return RES.no("该租户暂无支付配置");
        }
        return RES.ok(config);
    }
}
