package com.longsheng.tools.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_permission_info")
public class SysPermissionInfo extends Model<SysPermissionInfo> {


    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableField("name")
    private String name;

    @TableField("pid")
    private Integer pid;

    @TableField("sort")
    private Integer sort;

    @TableField("path")
    private String path;

    @TableField("component")
    private String component;

    @TableField("is_frame")
    private Integer isFrame;

    @TableField("is_cache")
    private Integer isCache;

    @TableField("type")
    private String type;

    @TableField("visible")
    private Integer visible;

    @TableField("sign")
    private String sign;

    @TableField("icon")
    private String icon;

    @TableField("remark")
    private String remark;

    @TableField("create_user")
    private Integer createUser;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_user")
    private Integer updateUser;

    @TableField("update_time")
    private Date updateTime;

    @TableField("del_flag")
    @TableLogic
    private Integer delFlag;


    @Override
    public Serializable pkVal() {
        return this.id;
    }

}
