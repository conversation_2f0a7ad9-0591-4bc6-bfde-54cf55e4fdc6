package com.longsheng.tools.system.service;

import com.longsheng.tools.system.vo.TableInfo;
import com.longsheng.tools.common.utils.RES;
import com.longsheng.tools.system.entity.TenantPayConfig;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 租户支付配置表 服务类
 * </p>
 *
 * <AUTHOR>
 */
public interface TenantPayConfigService extends IService<TenantPayConfig> {

    /**
     * 查询租户支付配置列表
     *
     * @param tenantPayConfig 租户支付配置
     * @param isExport 是否导出，0-否，1-是
     * @return 分页结果
     */
    TableInfo list(TenantPayConfig tenantPayConfig, int isExport);

    /**
     * 获取租户支付配置详情
     *
     * @param id 租户支付配置ID
     * @return 租户支付配置信息
     */
    RES getTenantPayConfig(Integer id);

    /**
     * 新增租户支付配置
     *
     * @param tenantPayConfig 租户支付配置信息
     * @return 结果
     */
    RES add(TenantPayConfig tenantPayConfig);

    /**
     * 修改租户支付配置
     *
     * @param tenantPayConfig 租户支付配置信息
     * @return 结果
     */
    RES update(TenantPayConfig tenantPayConfig);

    /**
     * 删除租户支付配置
     *
     * @param ids 需要删除的租户支付配置ID数组
     * @return 结果
     */
    RES delete(Integer[] ids);

    /**
     * 根据租户ID获取支付配置
     *
     * @param tenantId 租户ID
     * @return 支付配置信息
     */
    TenantPayConfig getByTenantId(Integer tenantId);
}
