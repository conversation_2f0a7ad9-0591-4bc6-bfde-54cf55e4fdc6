package com.longsheng.tools.modules.commonExpre.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.sql.Date;

@Data
public class UserCommonByUserIdVo {
    private String toolName;
    private String ceType;
    private String content;
    private Integer useCount;
    private Boolean isDefault;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;
}
