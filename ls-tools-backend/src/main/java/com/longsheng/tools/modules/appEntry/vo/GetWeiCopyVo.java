package com.longsheng.tools.modules.appEntry.vo;

import com.longsheng.tools.common.interfaces.BeanAdapter;
import com.longsheng.tools.modules.appEntry.entity.WeiCopy;
import lombok.Data;

@Data
public class GetWeiCopyVo implements BeanAdapter<WeiCopy, GetWeiCopyVo> {
    private String content;
    private String url;

    @Override
    public GetWeiCopyVo adapt(WeiCopy weiCopy) {
        this.content = weiCopy.getContent();
        this.url = weiCopy.getUrl();
        return this;
    }
}
