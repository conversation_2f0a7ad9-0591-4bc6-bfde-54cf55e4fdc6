<template>
  <div class="app-container">
    <!-- 用户配额和使用量导航 -->
    <div class="quota-navigation">
      <el-card shadow="never" class="quota-card">
        <div class="quota-header">
          <h3 class="quota-title">我的文章配额</h3>
          <el-button type="primary" size="small" icon="el-icon-money" @click="handleRecharge">充值</el-button>
        </div>
        <div class="quota-content">
          <div class="quota-item">
            <div class="quota-label">已使用：</div>
            <div class="quota-value">{{ usageInfo.totalArticlesGenerated || 0 }}</div>
          </div>
          <div class="quota-item">
            <div class="quota-label">总配额：</div>
            <div class="quota-value">{{ usageInfo.totalArticlesAllowed || 0 }}</div>
          </div>
          <div class="quota-item quota-progress">
            <div class="quota-label">使用率：</div>
            <div class="quota-progress-bar">
              <el-progress
                :percentage="usagePercentage"
                :status="usageStatus"
                :show-text="false"
              ></el-progress>
              <span class="progress-text">{{ usagePercentage }}%</span>
            </div>
          </div>
          <div class="quota-item">
            <div class="quota-label">剩余配额：</div>
            <div class="quota-value remaining">{{ remainingQuota }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="我的文章" name="articles">
        <el-table v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" width="80" />
          <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
          <el-table-column label="作者" align="center" prop="author" width="100" />
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
                {{ scope.row.status === 1 ? '已发布' : '未发布' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleViewArticle(scope.row)"
              >查看</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-promotion"
                @click="handlePublishArticle(scope.row)"
                v-if="scope.row.status === 0"
              >发布</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="articleTotal>0"
          :total="articleTotal"
          :page.sync="articleQuery.pageNum"
          :limit.sync="articleQuery.pageSize"
          @pagination="getArticleList"
        />
      </el-tab-pane>

      <el-tab-pane label="发布记录" name="publish">
        <el-table v-loading="loading" :data="publishList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" width="80" />
          <el-table-column label="文章标题" align="center" prop="articleTitle" :show-overflow-tooltip="true" />
          <el-table-column label="公众号" align="center" prop="officialAccountName" width="120" />
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag :type="getPublishStatusType(scope.row.status)">
                {{ getPublishStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="发布时间" align="center" prop="publishTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.publishTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleViewPublish(scope.row)"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="publishTotal>0"
          :total="publishTotal"
          :page.sync="publishQuery.pageNum"
          :limit.sync="publishQuery.pageSize"
          @pagination="getPublishList"
        />
      </el-tab-pane>

      <el-tab-pane label="我的公众号" name="accounts">
        <div class="account-toolbar">
          <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddAccount">添加公众号</el-button>
        </div>
        <el-table v-loading="loading" :data="accountList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" width="80" />
          <el-table-column label="公众号名称" align="center" prop="name" :show-overflow-tooltip="true" />
          <el-table-column label="AppID" align="center" prop="appId" width="180" />
          <el-table-column label="类型" align="center" prop="type" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.type === 1 ? 'success' : 'info'">
                {{ scope.row.type === 1 ? '服务号' : '订阅号' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" prop="status" width="100">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleAccountStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleEditAccount(scope.row)"
              >编辑</el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDeleteAccount(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="accountTotal>0"
          :total="accountTotal"
          :page.sync="accountQuery.pageNum"
          :limit.sync="accountQuery.pageSize"
          @pagination="getAccountList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 文章详情对话框 -->
    <el-dialog :title="articleDetailTitle" :visible.sync="articleDetailOpen" width="800px" append-to-body>
      <div class="article-detail" v-if="articleDetail">
        <h1 class="article-title">{{ articleDetail.title }}</h1>
        <div class="article-meta">
          <span>作者：{{ articleDetail.author }}</span>
          <span>创建时间：{{ parseTime(articleDetail.createTime) }}</span>
        </div>
        <div class="article-content" v-html="articleDetail.content"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="articleDetailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 发布记录详情对话框 -->
    <el-dialog :title="publishDetailTitle" :visible.sync="publishDetailOpen" width="800px" append-to-body>
      <div class="publish-detail" v-if="publishDetail">
        <h1 class="article-title">{{ publishDetail.articleTitle }}</h1>
        <div class="article-meta">
          <span>公众号：{{ publishDetail.officialAccountName }}</span>
          <span>发布时间：{{ parseTime(publishDetail.publishTime) }}</span>
          <span>状态：{{ getPublishStatusText(publishDetail.status) }}</span>
        </div>
        <div class="article-content" v-html="publishDetail.articleContent"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="publishDetailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 发布文章对话框 -->
    <el-dialog :title="'发布文章 - ' + (publishForm.title || '')" :visible.sync="publishOpen" width="500px" append-to-body>
      <el-form ref="publishForm" :model="publishForm" :rules="publishRules" label-width="100px">
        <el-form-item label="选择公众号" prop="wxAppIds">
          <el-select v-model="publishForm.wxAppIds" multiple placeholder="请选择公众号" style="width: 100%">
            <el-option
              v-for="item in officialAccounts"
              :key="item.appId"
              :label="item.name"
              :value="item.appId"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPublish" :loading="publishLoading">确 定</el-button>
        <el-button @click="publishOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 公众号表单对话框 -->
    <el-dialog :title="accountFormTitle" :visible.sync="accountOpen" width="600px" append-to-body>
      <el-form ref="accountForm" :model="accountForm" :rules="accountRules" label-width="100px">
        <el-form-item label="公众号名称" prop="name">
          <el-input v-model="accountForm.name" placeholder="请输入公众号名称" />
        </el-form-item>
        <el-form-item label="AppID" prop="appId">
          <el-input v-model="accountForm.appId" placeholder="请输入AppID" />
        </el-form-item>
        <el-form-item label="AppSecret" prop="appSecret">
          <el-input v-model="accountForm.appSecret" placeholder="请输入AppSecret" type="password" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="accountForm.type">
            <el-radio :label="1">服务号</el-radio>
            <el-radio :label="2">订阅号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="accountForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAccountForm" :loading="accountLoading">确 定</el-button>
        <el-button @click="accountOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 充值对话框 -->
    <ArticlePayment
      :visible.sync="rechargeDialogVisible"
      @close="handleRechargeClose"
    />
  </div>
</template>

<script>
import {
  listMyArticles,
  listMyArticlePublish,
  listMyOfficialAccounts,
  getMyOfficialAccounts,
  getArticleDetail,
  getPublishDetail,
  getOfficialAccountDetail,
  addOfficialAccount,
  updateOfficialAccount,
  delOfficialAccount,
  publishArticle
} from '@/api/system/article-user-content'
import {
  getMyArticleUsage
} from '@/api/system/article-user'
import ArticlePayment from './components/ArticlePayment.vue'

export default {
  name: "UserArticle",
  components: {
    ArticlePayment
  },
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'articles',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,

      // 用户配额和使用量信息
      usageInfo: {
        totalArticlesGenerated: 0,
        totalArticlesAllowed: 0,
        lastResetDate: null
      },
      // 充值对话框
      rechargeDialogVisible: false,

      // 文章列表相关
      articleList: [],
      articleTotal: 0,
      articleQuery: {
        pageNum: 1,
        pageSize: 10
      },

      // 发布记录列表相关
      publishList: [],
      publishTotal: 0,
      publishQuery: {
        pageNum: 1,
        pageSize: 10
      },

      // 公众号列表相关
      accountList: [],
      accountTotal: 0,
      accountQuery: {
        pageNum: 1,
        pageSize: 10
      },

      // 文章详情相关
      articleDetailOpen: false,
      articleDetailTitle: '文章详情',
      articleDetail: null,

      // 发布记录详情相关
      publishDetailOpen: false,
      publishDetailTitle: '发布记录详情',
      publishDetail: null,

      // 发布文章相关
      publishOpen: false,
      publishLoading: false,
      publishForm: {
        id: undefined,
        title: '',
        wxAppIds: []
      },
      publishRules: {
        wxAppIds: [
          { required: true, message: '请选择至少一个公众号', trigger: 'change' }
        ]
      },
      officialAccounts: [],

      // 公众号表单相关
      accountOpen: false,
      accountLoading: false,
      accountFormTitle: '',
      accountForm: {
        id: undefined,
        name: '',
        appId: '',
        appSecret: '',
        type: 1,
        status: 1,
        tenantId: undefined,
        sysUserId: undefined
      },
      accountRules: {
        name: [
          { required: true, message: '公众号名称不能为空', trigger: 'blur' }
        ],
        appId: [
          { required: true, message: 'AppID不能为空', trigger: 'blur' }
        ],
        appSecret: [
          { required: true, message: 'AppSecret不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择公众号类型', trigger: 'change' }
        ]
      },

    };
  },
  computed: {
    // 使用率百分比
    usagePercentage() {
      if (!this.usageInfo || !this.usageInfo.totalArticlesAllowed) return 0
      return Math.min(100, Math.round((this.usageInfo.totalArticlesGenerated / this.usageInfo.totalArticlesAllowed) * 100))
    },
    // 使用率状态
    usageStatus() {
      if (!this.usageInfo) return ''
      const percentage = this.usagePercentage
      if (percentage >= 90) return 'exception'
      if (percentage >= 70) return 'warning'
      return 'success'
    },
    // 剩余配额
    remainingQuota() {
      if (!this.usageInfo) return 0
      return Math.max(0, (this.usageInfo.totalArticlesAllowed || 0) - (this.usageInfo.totalArticlesGenerated || 0))
    }
  },
  created() {
    // 不再需要在前端存储用户ID，后端会自动获取

    // 加载用户配额信息
    this.getUserUsageInfo();
    // 加载文章列表
    this.getArticleList();
  },
  methods: {
    /** 获取用户配额使用情况 */
    getUserUsageInfo() {
      getMyArticleUsage().then(response => {
        if (response.code === 200 && response.data) {
          this.usageInfo = response.data;
        } else {
          this.usageInfo = {
            totalArticlesGenerated: 0,
            totalArticlesAllowed: 0,
            lastResetDate: null
          };
        }
      }).catch(() => {
        this.usageInfo = {
          totalArticlesGenerated: 0,
          totalArticlesAllowed: 0,
          lastResetDate: null
        };
      });
    },

    /** 处理充值按钮点击 */
    handleRecharge() {
      this.rechargeDialogVisible = true;
    },

    /** 充值对话框关闭回调 */
    handleRechargeClose() {
      this.rechargeDialogVisible = false;
      // 充值完成后刷新配额信息
      this.getUserUsageInfo();
    },

    /** 查询文章列表 */
    getArticleList() {
      this.loading = true;
      listMyArticles(this.articleQuery).then(response => {
        this.articleList = response.rows;
        this.articleTotal = response.total;
        this.loading = false;
      });
    },

    /** 查询发布记录列表 */
    getPublishList() {
      this.loading = true;
      listMyArticlePublish(this.publishQuery).then(response => {
        this.publishList = response.rows;
        this.publishTotal = response.total;
        this.loading = false;
      });
    },

    /** 查询公众号列表 */
    getAccountList() {
      this.loading = true;
      listMyOfficialAccounts(this.accountQuery).then(response => {
        this.accountList = response.rows;
        this.accountTotal = response.total;
        this.loading = false;
      });
    },

    /** 获取所有公众号（用于发布文章选择） */
    getOfficialAccounts() {
      getMyOfficialAccounts().then(response => {
        if (response.code === 200 && response.data) {
          this.officialAccounts = response.data;
        }
      });
    },

    /** 标签页切换 */
    handleTabClick(tab) {
      if (tab.name === 'articles') {
        this.getArticleList();
      } else if (tab.name === 'publish') {
        this.getPublishList();
      } else if (tab.name === 'accounts') {
        this.getAccountList();
      }
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 查看文章详情 */
    handleViewArticle(row) {
      getArticleDetail(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.articleDetail = response.data;
          this.articleDetailOpen = true;
        } else {
          this.$message.error('获取文章详情失败');
        }
      });
    },

    /** 查看发布记录详情 */
    handleViewPublish(row) {
      getPublishDetail(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.publishDetail = response.data;
          this.publishDetailOpen = true;
        } else {
          this.$message.error('获取发布记录详情失败');
        }
      });
    },

    /** 发布文章 */
    handlePublishArticle(row) {
      this.publishForm = {
        id: row.id,
        title: row.title,
        wxAppIds: []
      };

      // 获取可用的公众号列表
      this.getOfficialAccounts();

      this.publishOpen = true;
    },

    /** 提交发布 */
    submitPublish() {
      this.$refs.publishForm.validate(valid => {
        if (valid) {
          this.publishLoading = true;
          publishArticle(this.publishForm.id, this.publishForm.wxAppIds).then(response => {
            if (response.code === 200) {
              this.msgSuccess('发布成功');
              this.publishOpen = false;
              // 刷新文章列表
              this.getArticleList();
            } else {
              this.msgError(response.msg || '发布失败');
            }
            this.publishLoading = false;
          }).catch(() => {
            this.publishLoading = false;
          });
        }
      });
    },

    /** 添加公众号 */
    handleAddAccount() {
      this.accountForm = {
        id: undefined,
        name: '',
        appId: '',
        appSecret: '',
        type: 1,
        status: 1,
        tenantId: this.$store.state.user.tenantId,
        userId: this.currentUserId
      };
      this.accountFormTitle = '添加公众号';
      this.accountOpen = true;
    },

    /** 编辑公众号 */
    handleEditAccount(row) {
      getOfficialAccountDetail(row.id).then(response => {
        if (response.code === 200 && response.data) {
          this.accountForm = response.data;
          this.accountFormTitle = '编辑公众号';
          this.accountOpen = true;
        } else {
          this.$message.error('获取公众号详情失败');
        }
      });
    },

    /** 删除公众号 */
    handleDeleteAccount(row) {
      this.$confirm('确认删除该公众号吗？').then(() => {
        delOfficialAccount(row.id).then(response => {
          if (response.code === 200) {
            this.msgSuccess('删除成功');
            this.getAccountList();
          } else {
            this.msgError(response.msg || '删除失败');
          }
        });
      }).catch(() => {});
    },

    /** 提交公众号表单 */
    submitAccountForm() {
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          this.accountLoading = true;
          if (this.accountForm.id) {
            updateOfficialAccount(this.accountForm).then(response => {
              if (response.code === 200) {
                this.msgSuccess('修改成功');
                this.accountOpen = false;
                this.getAccountList();
              } else {
                this.msgError(response.msg || '修改失败');
              }
              this.accountLoading = false;
            }).catch(() => {
              this.accountLoading = false;
            });
          } else {
            addOfficialAccount(this.accountForm).then(response => {
              if (response.code === 200) {
                this.msgSuccess('添加成功');
                this.accountOpen = false;
                this.getAccountList();
              } else {
                this.msgError(response.msg || '添加失败');
              }
              this.accountLoading = false;
            }).catch(() => {
              this.accountLoading = false;
            });
          }
        }
      });
    },

    /** 公众号状态修改 */
    handleAccountStatusChange(row) {
      const text = row.status === 1 ? '启用' : '停用';
      this.$confirm('确认要"' + text + '"该公众号吗？').then(() => {
        // 不需要在前端设置用户ID，后端会自动获取
        updateOfficialAccount(row).then(response => {
          if (response.code === 200) {
            this.msgSuccess(text + '成功');
          } else {
            this.msgError(response.msg || text + '失败');
            row.status = row.status === 1 ? 0 : 1;
          }
        }).catch(() => {
          row.status = row.status === 1 ? 0 : 1;
        });
      }).catch(() => {
        row.status = row.status === 1 ? 0 : 1;
      });
    },

    /** 获取发布状态类型 */
    getPublishStatusType(status) {
      switch (status) {
        case 0: return 'info';
        case 1: return 'success';
        case 2: return 'warning';
        case 3: return 'danger';
        default: return 'info';
      }
    },

    /** 获取发布状态文本 */
    getPublishStatusText(status) {
      switch (status) {
        case 0: return '待发布';
        case 1: return '发布成功';
        case 2: return '发布中';
        case 3: return '发布失败';
        default: return '未知状态';
      }
    },

  }
};
</script>

<style scoped>
/* 配额导航样式 */
.quota-navigation {
  margin-bottom: 20px;
}

.quota-card {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.quota-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.quota-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.quota-content {
  display: flex;
  align-items: center;
  gap: 32px;
  flex-wrap: wrap;
}

.quota-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quota-progress {
  flex: 1;
  min-width: 200px;
}

.quota-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.quota-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.quota-value.remaining {
  color: #67c23a;
}

.quota-progress-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

/* 原有样式 */
.account-toolbar {
  margin-bottom: 20px;
}

.article-detail, .publish-detail {
  padding: 0 20px;
}

.article-title {
  font-size: 24px;
  text-align: center;
  margin-bottom: 20px;
}

.article-meta {
  display: flex;
  justify-content: space-around;
  color: #666;
  margin-bottom: 20px;
  font-size: 14px;
}

.article-content {
  line-height: 1.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quota-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .quota-progress {
    width: 100%;
    min-width: auto;
  }

  .quota-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
