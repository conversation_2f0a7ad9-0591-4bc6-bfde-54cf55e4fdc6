import request from '@/utils/request'

const baseUrl = '/product/';

// 查询商品管理列表
export function listProduct(query) {
  return request({
    url: baseUrl + 'list',
    method: 'get',
    params: query
  })
}

// 查询商品管理详细
export function getProduct(id) {
  return request({
    url: baseUrl + id,
    method: 'get'
  })
}

// 新增商品管理
export function addProduct(data) {
  return request({
    url: baseUrl,
    method: 'post',
    data: data
  })
}

// 修改商品管理
export function updateProduct(data) {
  return request({
    url: baseUrl,
    method: 'put',
    data: data
  })
}

// 删除商品管理
export function delProduct(id) {
  return request({
    url: baseUrl + id,
    method: 'delete'
  })
}

// 导出商品管理
export function exportProduct(query) {
  return request({
    url: baseUrl + 'export',
    method: 'get',
    params: query
  })
}

export function changeProductStatus(id) {
  return request({
    url: baseUrl + 'changeProductStatus/' + id,
    method: 'post'
  })
}
