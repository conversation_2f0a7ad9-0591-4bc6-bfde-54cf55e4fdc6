import request from '@/utils/request'

const baseUrl = '/weiCopy';

export function getWeiCopyList(query) {
  return request({
    url: baseUrl + '/getWeiCopyList',
    method: 'get',
    params: query
  })
}

export function getWeiCopyById(id) {
  return request({
    url: baseUrl + "/getWeiCopyById" +"/" +id,
    method: 'get'
  })
}


export function addWeiCopy(data) {
  return request({
    url: baseUrl+"/addWeiCopy",
    method: 'post',
    data: data
  })
}

export function updateWeiCopyById(data) {
  return request({
    url: baseUrl + '/updateWeiCopyById',
    method: 'post',
    data: data
  })
}

export function deleteWeiCopy(data) {
  return request({
    url: baseUrl + '/deleteWeiCopy',
    method: 'post',
    data
  })
}


export function getTemplate(query) {
  if(query == null){
    return request({
      url: baseUrl + '/getTemplate',
      method: 'get',
      responseType: 'blob',
    })
  }else{
    let data = {
      tenantId: query
    }
    return request({
      url: baseUrl + '/getTemplate',
      method: 'get',    
      responseType: 'blob',
      params: data
    })
  }
}
